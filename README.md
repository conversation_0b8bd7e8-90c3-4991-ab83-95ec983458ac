# Libra AI

<div align="center">

<img src="/logo.svg" alt="Libra logo" style="width: 250px; height: auto; max-width: 100%;" />

**Open Source Alternative to V0/Lovable**

**Libra AI: Language as Application** - Launch, iterate, and deploy your next web application with a single sentence.

[![SPONSORED BY CLERK](https://img.shields.io/badge/SPONSORED%20BY-CLERK-6C47FF?style=for-the-badge)](https://clerk.com?utm_source=libra.dev)
[![SPONSORED BY E2B FOR STARTUPS](https://img.shields.io/badge/SPONSORED%20BY-E2B%20FOR%20STARTUPS-ff8800?style=for-the-badge)](https://e2b.dev/startups?utm_source=libra.dev)
[![SPONSORED BY POSTHOG FOR STARTUPS](https://img.shields.io/badge/SPONSORED%20BY-POSTHOG%20FOR%20STARTUPS-1D4AFF?style=for-the-badge)](https://posthog.com/startups?utm_source=libra.dev)
[![SPONSORED BY DAYTONA STARTUP GRID](https://img.shields.io/badge/SPONSORED%20BY-DAYTONA%20STARTUP%20GRID-2ECC71?style=for-the-badge)](https://daytona.io/startups?utm_source=libra.dev)
[![SPONSORED BY CLOUDFLARE FOR STARTUPS](https://img.shields.io/badge/SPONSORED%20BY-CLOUDFLARE%20FOR%20STARTUPS-F38020?style=for-the-badge)](https://www.cloudflare.com/forstartups/?utm_source=libra.dev)

[![Open Source License](https://img.shields.io/badge/License-AGPL-green.svg)](https://github.com/nextify-limited/libra/blob/main/LICENSE) [![Made by Nextify2024](https://img.shields.io/badge/made_by-nextify2024-blue?color=FF782B&link=https://x.com/nextify2024)](https://x.com/nextify2024)

[🌐 **Try Now**](https://libra.dev) • [📖 **Documentation**](https://docs.libra.dev/) • [💬 **Join Community**](https://forum.libra.dev) • [⚡ **View Source**](https://github.com/nextify-limited/libra)

[🇨🇳 **中文版**](README_ZH.md)

</div>

- [Libra AI](#libra-ai)
  - [🚀 What is Libra AI?](#-what-is-libra-ai)
    - [✨ Core Features](#-core-features)
      - [🤖 AI-Powered Coding](#-ai-powered-coding)
      - [🛠️ Integrated Development Experience](#️-integrated-development-experience)
      - [🔗 Full-Stack Integration](#-full-stack-integration)
      - [🌐 Production Deployment](#-production-deployment)
    - [Why Open Source?](#why-open-source)
  - [🏗️ Technical Architecture](#️-technical-architecture)
    - [Compute \& Runtime](#compute--runtime)
    - [Data Storage](#data-storage)
    - [Network \& Security](#network--security)
    - [Development Tools \& Services](#development-tools--services)
    - [Application Services Overview](#application-services-overview)
    - [🛠️ Core Technology Stack](#️-core-technology-stack)
      - [Frontend Technology Architecture](#frontend-technology-architecture)
      - [Backend \& API Architecture](#backend--api-architecture)
      - [AI \& Machine Learning](#ai--machine-learning)
      - [Data Storage Architecture](#data-storage-architecture)
      - [Deployment \& Infrastructure](#deployment--infrastructure)
      - [Development Toolchain](#development-toolchain)
  - [⚡ Quick Start](#-quick-start)
    - [🎯 Choose Your Usage Method](#-choose-your-usage-method)
      - [🌐 Cloud Hosted Service (Recommended)](#-cloud-hosted-service-recommended)
      - [💻 Local Development Deployment (Developers)](#-local-development-deployment-developers)
    - [📦 Environment Requirements](#-environment-requirements)
    - [🚀 Local Environment Setup](#-local-environment-setup)
      - [Step 1: Get Source Code](#step-1-get-source-code)
      - [Step 2: Configure Environment Variables](#step-2-configure-environment-variables)
      - [Step 3: Initialize Database](#step-3-initialize-database)
      - [Step 4: Start Development Services](#step-4-start-development-services)
      - [Step 5: Configure Stripe Payment (Required)](#step-5-configure-stripe-payment-required)
    - [🌐 Local Service Addresses](#-local-service-addresses)
  - [🚀 Deployment Options](#-deployment-options)
    - [🌐 Cloud Hosted Service (Recommended)](#-cloud-hosted-service-recommended-1)
    - [🏠 Self-Hosted Deployment](#-self-hosted-deployment)
  - [🎯 Hosted Platform vs Open Source](#-hosted-platform-vs-open-source)
    - [📊 Feature Comparison](#-feature-comparison)
    - [🤔 How to Choose the Right Version?](#-how-to-choose-the-right-version)
      - [Choose Cloud Hosting if you](#choose-cloud-hosting-if-you)
      - [Choose Open Source if you](#choose-open-source-if-you)
  - [❓ Frequently Asked Questions](#-frequently-asked-questions)
    - [🆚 Product Versions](#-product-versions)
    - [🛠️ Technical Issues](#️-technical-issues)
    - [💼 Commercial Use](#-commercial-use)
    - [🔧 Development \& Deployment](#-development--deployment)
    - [🤝 Community Contribution](#-community-contribution)
      - [🌟 Usage \& Promotion](#-usage--promotion)
      - [🔧 Code Contribution](#-code-contribution)
      - [📝 Other Contribution Methods](#-other-contribution-methods)
      - [🎯 Contribution Guidelines](#-contribution-guidelines)
  - [📄 Open Source License](#-open-source-license)
    - [📜 AGPL-3.0 Open Source License](#-agpl-30-open-source-license)
      - [✅ Your Rights](#-your-rights)
      - [📋 Your Obligations](#-your-obligations)
      - [💼 Commercial License](#-commercial-license)
    - [💬 Participate in Roadmap Discussion](#-participate-in-roadmap-discussion)
    - [🙏 Acknowledgments](#-acknowledgments)

---

## 🚀 What is Libra AI?

**Libra AI** is a production-ready AI-native development platform that enables full lifecycle management of web applications through natural language interaction. Built with modern technology architecture, it covers the complete engineering process from rapid prototyping to enterprise-grade production deployment.

Just as V0 is deeply integrated with the Vercel ecosystem, Libra is specifically designed for the Cloudflare Workers architecture, providing a native AI development experience.

### ✨ Core Features

#### 🤖 AI-Powered Coding

- Multi-model integration: Claude, OpenAI, Gemini, DeepSeek, and more
- Natural language-driven production-grade code generation
- Intelligent context awareness and best practice adherence
- Multi-sandbox provider support (E2B, Daytona)

#### 🛠️ Integrated Development Experience

- Cloud IDE: syntax highlighting, smart indentation, custom plugins
- Hot Module Replacement (HMR) real-time preview
- Intelligent dependency analysis and automatic installation

#### 🔗 Full-Stack Integration

- Seamless GitHub integration with one-way sync
- Cloudflare edge computing deployment
- Enterprise-grade identity authentication (OAuth 2.0)
- Stripe commercial subscription management

#### 🌐 Production Deployment

- Cloudflare Workers edge computing network
- Serverless architecture with elastic scaling
- Automated TLS/SSL certificate management
- Git version control with one-click rollback

### Why Open Source?

- 📂 **Technical Autonomy**: Avoid vendor lock-in risks
- 🔧 **Architectural Flexibility**: Support for deep customization and extension
- 💝 **Community Ecosystem**: Open source community collaboration

## 🏗️ Technical Architecture

Libra is built entirely on Cloudflare. You need to be familiar with the following products:

### Compute & Runtime

| Product Name | Description | Application in Libra |
|-------------|-------------|---------------------|
| [Workers](https://developers.cloudflare.com/workers/?utm_source=libra.dev) | Serverless computing platform | Core application runtime environment, hosting all service logic |
| [Durable Objects](https://developers.cloudflare.com/durable-objects/?utm_source=libra.dev) | Strong consistency storage | Real-time state management and session persistence |
| [Browser Rendering](https://developers.cloudflare.com/browser-rendering/?utm_source=libra.dev) | Browser rendering service | Web screenshot generation and preview functionality |
| [Workers for Platforms](https://developers.cloudflare.com/cloudflare-for-platforms/?utm_source=libra.dev) | Multi-tenant platform | User project isolation deployment and routing management |

### Data Storage

| Product Name | Description | Application in Libra |
|-------------|-------------|---------------------|
| [KV](https://developers.cloudflare.com/kv/?utm_source=libra.dev) | Global key-value storage | Configuration caching and temporary data storage |
| [D1](https://developers.cloudflare.com/d1/?utm_source=libra.dev) | Serverless SQLite database | Authentication data and lightweight business data |
| [Hyperdrive](https://developers.cloudflare.com/hyperdrive/?utm_source=libra.dev) | Database connection acceleration | PostgreSQL connection pooling and query optimization |
| [R2](https://developers.cloudflare.com/r2/?utm_source=libra.dev) | Object storage service | File uploads, static assets, and build artifact storage |

### Network & Security

| Product Name | Description | Application in Libra |
|-------------|-------------|---------------------|
| [Turnstile](https://developers.cloudflare.com/turnstile/?utm_source=libra.dev) | Smart CAPTCHA | Security verification for user registration and sensitive operations |
| [Cloudflare for SaaS](https://developers.cloudflare.com/cloudflare-for-platforms/cloudflare-for-saas/?utm_source=libra.dev) | Custom domain management | Custom domain binding and SSL certificates for user projects |

### Development Tools & Services

| Product Name | Description | Application in Libra |
|-------------|-------------|---------------------|
| [Workflows](https://developers.cloudflare.com/workflows/?utm_source=libra.dev) | Workflow orchestration | Step-by-step management of complex deployment processes |
| [Queues](https://developers.cloudflare.com/queues/?utm_source=libra.dev) | Message queue service | Asynchronous task processing and batch deployment management |
| [AI Gateway](https://developers.cloudflare.com/ai-gateway/?utm_source=libra.dev) | AI model gateway | Monitor and control your AI applications |
| [Images](https://developers.cloudflare.com/images/?utm_source=libra.dev) | Image processing optimization | Dynamic image transformation and CDN distribution |

Libra adopts **Turborepo** Monorepo architecture design:

```text
libra/
├── apps/                    # Core application services
│   ├── auth-studio/         # Authentication management console (D1 + drizzle-kit)
│   ├── builder/             # Vite build service - code compilation and deployment
│   ├── cdn/                 # Hono CDN service - static asset management
│   ├── deploy/              # Deployment service V2 - Cloudflare Queues
│   ├── deploy-workflow/     # Deployment service V1 - Cloudflare Workflows (deprecated)
│   ├── dispatcher/          # Request routing dispatcher (Workers for Platforms)
│   ├── docs/                # Technical documentation site (Next.js + FumaDocs)
│   ├── email/               # Email service previewer (React Email)
│   ├── opennext-cache/      # OpenNext cache service (Cloudflare)
│   ├── screenshot/          # Screenshot service - Cloudflare Queues
│   ├── vite-shadcn-template/# Project template engine (Vite + shadcn/ui)
│   └── web/                 # Next.js 15 main application (React 19)
├── packages/                # Shared package modules
│   ├── api/                 # API layer (tRPC + type safety)
│   ├── auth/                # Authentication service (better-auth)
│   ├── better-auth-cloudflare/ # Cloudflare authentication adapter
│   ├── better-auth-stripe/  # Stripe payment integration
│   ├── common/              # Common utility library and type definitions
│   ├── db/                  # Main database schema and operations (PostgreSQL)
│   ├── email/               # Email service components
│   ├── middleware/          # Middleware services and tools
│   ├── sandbox/             # Unified sandbox abstraction layer (E2B + Daytona)
│   ├── shikicode/           # Code editor (Shiki syntax highlighting)
│   ├── templates/           # Project scaffolding templates
│   └── ui/                  # Design system (shadcn/ui + Tailwind CSS v4)
├── tooling/                 # Development tools and configuration
│   └── typescript-config/   # Shared TypeScript configuration
└── scripts/                 # GitHub environment variable management
```

### Application Services Overview

**🔒 Authentication Management Center (`apps/auth-studio`)**

- User, organization, permission, and subscription lifecycle management
- Main database uses PostgreSQL (via Neon + Hyperdrive), authentication data uses D1 (SQLite)
- OAuth 2.0 multi-provider authentication system
- Stripe commercial payment gateway integration

**🔨 Build Compilation Service (`apps/builder`)**

- Vite high-performance build engine with millisecond-level hot startup
- Automatic code compilation and seamless production deployment
- Multi-tech-stack project template rapid instantiation

**📺 Content Distribution Service (`apps/cdn`)**

- Hono framework-driven file management system
- Intelligent image processing and compression optimization
- Global CDN edge caching acceleration
- Cloudflare Workers edge deployment

**🚀 Deployment Service V2 (`apps/deploy`)**

- Modern deployment architecture based on Cloudflare Queues
- Batch processing and concurrency control
- D1 database state management
- Dead letter queue handling for failed deployments
- Comprehensive error handling and retry logic

**⚡ Deployment Service V1 (`apps/deploy-workflow`, deprecated)**

- Deployment orchestration based on Cloudflare Workflows
- Step-by-step deployment process and state management
- Built-in retry mechanism and error recovery
- Support for complex deployment scenarios and dependency management

**🔀 Request Routing Service (`apps/dispatcher`)**

- Workers for Platforms core routing component
- Route user domain requests to corresponding Worker instances
- Dynamic Worker scheduling and lifecycle management
- Custom domain binding and SSL certificate handling
- Unified authentication and access control layer
- Cloudflare SaaS for Platforms integration

**📖 Technical Documentation Site (`apps/docs`)**

- FumaDocs modern documentation solution
- Cloudflare Workers global distribution

**📧 Email Notification Service (`apps/email`)**

- React Email component-based email development
- Multi-scenario email template engine

**🔄 OpenNext Cache Service (`apps/opennext-cache`)**

- Next.js Cloudflare deployment cache optimization
- OpenNext framework and Cloudflare Workers integration
- Seamless integration with main web application deployment

**📸 Screenshot Service (`apps/screenshot`)**

- Cloudflare Queues-based screenshot generation
- Asynchronous queue processing for web screenshot requests
- Automated website preview image generation
- R2 storage for screenshot files
- Batch processing and error retry

**🔨 Project Scaffolding Service (`apps/vite-shadcn-template`)**

- Vite high-performance build toolchain
- Fast compilation and production deployment
- Pre-configured shadcn/ui + Tailwind CSS v4
- Visual template selection and preview

**🌐 Core Web Application (`apps/web`)**

- Next.js 15 App Router + React 19 technology stack
- Platform main interface and user interaction layer
- AI-driven intelligent conversation and project management
- Real-time code editing, preview, deployment, and version control
- Cloudflare Workers serverless deployment

### 🛠️ Core Technology Stack

#### Frontend Technology Architecture

| Technology Framework | Application Scenario | Version |
|---------------------|---------------------|---------|
| [Next.js](https://nextjs.org?utm_source=libra.dev) | React full-stack development framework (App Router) | 15.3.5 |
| [React](https://react.dev?utm_source=libra.dev) | User interface library (Server Components) | 19.1.1 |
| [TypeScript](https://typescriptlang.org?utm_source=libra.dev) | Statically typed JavaScript superset | 5.8.3+ |
| [Tailwind CSS](https://tailwindcss.com?utm_source=libra.dev) | Utility-first CSS framework | 4.1.11 |
| [shadcn/ui](https://ui.shadcn.com?utm_source=libra.dev) | Component library and design system | Based on Radix UI |
| [Radix UI](https://radix-ui.com?utm_source=libra.dev) | Unstyled accessible UI primitive library | 1.2.x-1.3.x |
| [Motion](https://motion.dev?utm_source=libra.dev) | Modern animation engine | 12.23.11 |
| [Lucide React](https://lucide.dev?utm_source=libra.dev) | Vector icon library | 0.486.0 |

#### Backend & API Architecture

| Technology Framework | Application Scenario | Version |
|---------------------|---------------------|---------|
| [tRPC](https://trpc.io?utm_source=libra.dev) | End-to-end type-safe API development | 11.4.3+ |
| [Hono](https://hono.dev?utm_source=libra.dev) | Edge computing web framework | 4.8.10+ |
| [Zod](https://zod.dev?utm_source=libra.dev) | TypeScript data validation library | 4.0.14 |
| [Drizzle ORM](https://orm.drizzle.team?utm_source=libra.dev) | Type-safe TypeScript ORM | 0.44.4 |
| [better-auth](https://better-auth.com?utm_source=libra.dev) | Modern identity authentication solution | 1.3.4 |

#### AI & Machine Learning

| AI Platform | Features | API Version |
|------------|----------|-------------|
| [AI SDK](https://sdk.vercel.ai?utm_source=libra.dev) | Multi-provider AI model integration | 4.3.19 |
| [E2B](https://e2b.dev?utm_source=libra.dev) | Secure code execution sandbox | 1.2.0-beta.5 |
| [Daytona](https://daytona.io?utm_source=libra.dev) | Development environment sandbox provider | Latest |
| **Integrated AI Models** | | |
| Anthropic Claude | Advanced reasoning and code generation | API v1 |
| Azure OpenAI | Enterprise-grade AI model service | API v1 |
| Google Gemini | Multimodal AI capabilities | API v1 |
| DeepSeek | Cost-effective code generation | API v1 |

#### Data Storage Architecture

| Database Technology | Application Scenario | Version |
|--------------------|---------------------|---------|
| [Neon](https://neon.com?utm_source=libra.dev) | Main database (PostgreSQL) | 17+ |
| [Cloudflare Hyperdrive](https://developers.cloudflare.com/hyperdrive?utm_source=libra.dev) | Database connection pooling and acceleration | Latest |
| [Cloudflare D1](https://developers.cloudflare.com/d1?utm_source=libra.dev) | Edge database (SQLite) | Latest |
| [Drizzle Kit](https://orm.drizzle.team/kit-docs/overview?utm_source=libra.dev) | Database migration tool | 0.31.4+ |

#### Deployment & Infrastructure

| Platform Technology | Application Scenario | Version |
|---------------------|---------------------|---------|
| [Cloudflare Workers](https://workers.cloudflare.com?utm_source=libra.dev) | Serverless edge computing platform | Latest |
| [OpenNext](https://opennext.js.org/cloudflare?utm_source=libra.dev) | Next.js Cloudflare deployment adapter | 1.6.2 |
| [Turborepo](https://turbo.build?utm_source=libra.dev) | High-performance Monorepo build system | 2.5.5 |
| [Bun](https://bun.sh?utm_source=libra.dev) | JavaScript runtime and package manager | 1.2.19 |

#### Development Toolchain

| Tool | Purpose | Version |
|------|---------|---------|
| [Biome](https://biomejs.dev?utm_source=libra.dev) | Code formatting and quality checking | ^2.2.2 |
| [Vitest](https://vitest.dev?utm_source=libra.dev) | Unit testing framework | 3.2.4 |
| [Paraglide.js](https://inlang.com/m/gerre34r/library-inlang-paraglideJs?utm_source=libra.dev) | Internationalization i18n solution | 2.2.0 |

## ⚡ Quick Start

### 🎯 Choose Your Usage Method

#### 🌐 Cloud Hosted Service (Recommended)

- Visit [libra.dev](https://libra.dev) to get started
- Quick registration with GitHub OAuth or email
- Build production-grade applications in minutes

#### 💻 Local Development Deployment (Developers)

- Complete source code access and control
- Deep customization development and extension
- Private/enterprise-grade deployment

### 📦 Environment Requirements

```bash
# System dependency requirements
git --version   # >= 2.30.0
node --version  # >= 20.0.0 (recommend 24)
bun --version   # >= 1.0.0
```

### 🚀 Local Environment Setup

#### Step 1: Get Source Code

```bash
git clone https://github.com/nextify-limited/libra.git
cd libra
bun install
# (Optional) Generate i18n files for apps/web
cd apps/web && bun run prebuild && cd ../..
```

#### Step 2: Configure Environment Variables

```bash
cp .env.example .env
```

Edit the `.env` file to configure necessary environment variables.

#### Step 3: Initialize Database

Main database (PostgreSQL) initialization:

```bash
# Generate and run migrations from project root
cd packages/db
bun db:generate
bun db:migrate
```

Authentication database (D1/SQLite) initialization:

```bash
# Test D1 database connection (local environment)
cd apps/web && bun wrangler d1 execute libra --local --command='SELECT 1'

# Generate and run authentication database migrations
cd packages/auth
bun db:generate
bun db:migrate
```

#### Step 4: Start Development Services

```bash
# Start all services
bun dev

# Or start main application separately
cd apps/web && bun dev
```

#### Step 5: Configure Stripe Payment (Required)

Configure corresponding products:

```bash
stripe listen --forward-to localhost:3000/api/auth/stripe/webhook
```

### 🌐 Local Service Addresses

After setup, you can access various services through the following addresses:

- **Core Application (web)**: <http://localhost:3000>
- **Email Preview (email)**: <http://localhost:3001>
- **Authentication Management (auth-studio)**: <http://localhost:3002>
- **Technical Documentation (docs)**: <http://localhost:3003>
- **CDN Service (cdn)**: <http://localhost:3004>
- **Build Service (builder)**: <http://localhost:5173> (Vite default port)
- **Template Service (vite-shadcn-template)**: <http://localhost:5173> (Vite default port, may conflict with builder)
- **Routing Service (dispatcher)**: <http://localhost:3007>
- **Deployment Service V2 (deploy)**: <http://localhost:3008>
- **Screenshot Service (screenshot)**: <http://localhost:3009>
- **Deployment Service V1 (deploy-workflow, deprecated)**: <http://localhost:3008> (shares port with Deployment Service V2)

## 🚀 Deployment Options

### 🌐 Cloud Hosted Service (Recommended)

Ready-to-use cloud development experience:

1. Visit [libra.dev](https://libra.dev) official platform
2. Quick registration with GitHub OAuth or email
3. AI-driven rapid application building
4. One-click deployment to custom domains

**Cloud Service Advantages:**
- Zero-configuration out-of-the-box
- Elastic scaling and automatic updates
- Built-in AI model integration
- Enterprise-grade technical support

### 🏠 Self-Hosted Deployment

**1. Deploy Libra Platform Itself**

All Libra platform services are deployed on Cloudflare Workers:

```bash

# Deploy various services to Cloudflare Workers
# Refer to the following workflow files for complete deployment process:
# - .github/workflows/web.yml - Main application deployment
# - .github/workflows/cdn.yml - CDN service deployment
# - .github/workflows/deploy.yml - Deployment service deployment
# - .github/workflows/dispatcher.yml - Routing service deployment
# - .github/workflows/screenshot.yml - Screenshot service deployment
# - .github/workflows/docs.yml - Documentation site deployment
```

**2. Deploy User Projects as PaaS Platform**

Libra uses **Workers for Platforms** technology to provide project deployment capabilities for users, offering two deployment service architectures:

1. **Workers for Platforms Architecture**:
    - Each user project is deployed as an independent Worker
    - Intelligent routing through `dispatcher` service
    - Support for custom domain binding
    - Completely isolated runtime environment (process-level isolation through Workers for Platforms)

2. **Deployment Service Architecture Options**:

   **V2 Queue Architecture** (`apps/deploy`):
    - **Cloudflare Queues**: Asynchronous queue processing for deployment tasks
    - **Batch Processing**: Support for concurrency control and batch deployment
    - **Dead Letter Queue**: Retry mechanism for handling failed deployments
    - **Use Cases**: High concurrency, large-scale deployment needs

   **V1 Workflow Architecture** (`apps/deploy-workflow`, deprecated):
    - **Cloudflare Workflows**: Step-by-step deployment orchestration
    - **State Persistence**: Built-in state management and recovery mechanism
    - **Complex Processes**: Support for complex deployment dependencies and conditional logic
    - **Use Cases**: Complex deployment processes, scenarios requiring precise control

3. **General Deployment Process**:
    - Verify user permissions and project quotas
    - Create sandbox environment for secure building (supports E2B or Daytona)
    - Sync project files
    - Execute build commands (`bun install` & `bun build`)
    - Deploy to user's Worker instance using Wrangler API
    - Update routing configuration, clean up temporary environment

4. **Technical Features**:
    - **Sandbox Environment**: Secure isolated build environment (supports E2B or Daytona)
    - **Global Distribution**: Leverage Cloudflare edge computing network
    - **Flexible Architecture**: Choose appropriate deployment service based on needs

## 🎯 Hosted Platform vs Open Source

### 📊 Feature Comparison

| Feature | Hosted Platform | Open Source | Notes |
|---------|----------------|-------------|-------|
| **🤖 AI Code Generation** | ✅ Out-of-the-box | ❌ Requires API key configuration | Multi-AI provider integration |
| **🔧 Development Environment** | ✅ Zero configuration | ❌ Requires sandbox environment setup | Cloud IDE and real-time preview |
| **📂 GitHub Integration** | ✅ One-click connection | ❌ Requires OAuth authorization setup | Repository auto-creation and sync |
| **🌐 Deployment Service** | ✅ Built-in support | ❌ Requires deployment environment setup | Cloudflare native integration |
| **🎨 Editor** | ✅ Full features | ❌ Basic visual editing | Visual editing and preview |
| **🔒 Data Control** | 🔒 Cloud hosted | ✅ Complete private control | Self-hosted data complete control |
| **🛠️ Custom Development** | ⚠️ Platform limitations | ✅ Unlimited customization | Source-level modification and extension |
| **📞 Technical Support** | ✅ Professional service | 🤝 Community support | Official service vs open source community |
| **💰 Cost Structure** | 💰 Pay-as-you-go | 🆓 Infrastructure costs | SaaS subscription vs self-maintenance |

### 🤔 How to Choose the Right Version?

#### Choose Cloud Hosting if you

- **🚀 Quick Launch**: No complex configuration needed, start application development immediately
- **💼 Business Priority**: Focus on product development rather than infrastructure operations
- **👥 Team Collaboration**: Need enterprise-grade team management features
- **📞 Professional Service**: Expect official technical support and SLA guarantees
- **🔄 Automatic Operations**: Want platform automated updates and maintenance

#### Choose Open Source if you

- **🏠 Data Autonomy**: Complete control over data storage and processing
- **🔧 Deep Customization**: Need to modify core functionality or integrate proprietary business logic
- **💰 Cost Control**: Have technical team, need to optimize long-term operational costs
- **🌍 Special Deployment**: Specific region/private network/offline environment deployment
- **📚 Technical Learning**: Deep understanding of complete technical architecture design

## ❓ Frequently Asked Questions

### 🆚 Product Versions

**Q: What are the core differences between the hosted platform and open source version?**

A: **Cloud hosting** is an official SaaS service, ready to use upon registration, including complete AI capabilities and enterprise-grade features. **Open source version** provides core source code, supports self-deployment and deep customization, but requires self-configuration of AI APIs and operational environment.

**Q: How complete are the features in the open source version?**

A: The open source version includes the platform's core functional architecture. We follow the principle of "99% features open source, 1% commercial services" to ensure developers can access complete technical capabilities.

### 🛠️ Technical Issues

**Q: What technical background is required to use Libra?**

A: Depends on usage method:

- **Hosted platform users**: No technical background required, just need to use a browser
- **Local developers**: Need basic web development knowledge and Node.js experience
- **Self-hosted deployment**: Need server and DevOps practical experience

**Q: How is the quality of AI-generated code?**

A: We always pursue production-grade code quality:

- Complete TypeScript type safety guarantee
- Follow modern React development patterns and industry best practices
- Responsive design implementation based on Tailwind CSS
- Accessible components integrated with Radix UI
- Clear and maintainable code structure and comments

**Q: Can I customize AI behavior and prompts?**

A: Full customization is supported in the open source version:

- Custom AI prompt engineering
- Flexible model selection logic configuration
- Modify code generation templates
- Integrate third-party AI services

### 💼 Commercial Use

**Q: Can Libra be used for commercial projects?**

A: Absolutely. We provide multiple commercial solutions:

- **Hosted platform**: Business-friendly pay-as-you-go model
- **Open source version**: Follows AGPL-3.0 open source license, requires derivative projects to also be open source
- **Commercial licensing**: Provides commercial open source licenses for enterprises requiring closed-source deployment

**Q: How about data security and privacy protection?**

A: We provide different levels of data protection solutions:

- **Hosted platform**: Data processed in our secure infrastructure, compliant with international security standards
- **Self-hosted deployment**: You have complete control over data storage, processing, and access permissions
- **Enterprise customization**: Can provide customized solutions based on special security requirements and compliance needs

**Q: Do you provide enterprise-grade technical support?**

A: Yes. Our enterprise services include:

- Private cloud environment deployment
- Custom feature development services
- Dedicated technical support team
- Service Level Agreement (SLA) guarantees
- Security auditing and compliance support

For enterprise services, please contact: [<EMAIL>](mailto:<EMAIL>)

### 🔧 Development & Deployment

**Q: Which deployment platforms are supported?**

A: Currently only supports deployment on Cloudflare

**Q: How to participate in open source project contributions?**

A: We warmly welcome community contributions. For specific methods, please refer to the [Community Contribution Guidelines](#-community-contribution) below.

### 🤝 Community Contribution

We warmly welcome contributions from developers worldwide! Here are the ways you can participate:

#### 🌟 Usage & Promotion

- Experience [Libra platform](https://libra.dev) and share your usage experience
- Create amazing application projects and showcase them in the community
- Write technical blog articles or create tutorial videos
- Share and recommend Libra on social media platforms

#### 🔧 Code Contribution

```bash
# 1. Fork our repository on GitHub
# 2. Clone your fork to local
git clone https://github.com/your-username/libra.git
cd libra

# 3. Create feature development branch
git checkout -b feature/your-amazing-feature

# 4. Develop and test thoroughly
bun install
bun dev

# 5. Commit code with clear commit messages
git commit -m "feat: add incredible new feature"

# 6. Push branch and create Pull Request
git push origin feature/your-amazing-feature
```

#### 📝 Other Contribution Methods

- **Documentation Improvement**: Improve usage guides, add example code, fix documentation errors
- **Issue Reporting**: Help us discover and locate system issues
- **Feature Suggestions**: Propose improvement ideas and new feature requirements
- **Multi-language Support**: Help translate Libra into more languages
    - Project uses Paraglide.js for internationalization
    - Translation file location: `apps/web/messages/[locale].json`
    - Add new language: Add language code in `apps/web/project.inlang/settings.json`
- **Community Support**: Help other users in [forum](https://forum.libra.dev) and GitHub discussions

#### 🎯 Contribution Guidelines

- Please follow our [Code of Conduct](https://github.com/nextify-limited/libra/blob/main/code_of_conduct_zh.md)
- Read and follow [Contribution Guidelines](https://github.com/nextify-limited/libra/blob/main/TECHNICAL_GUIDELINES_ZH.md)
- Use standardized commit message format (Conventional Commits)
- Provide corresponding test cases for new features
- Update relevant documentation in a timely manner

## 📄 Open Source License

### 📜 AGPL-3.0 Open Source License

Libra is released under the [GNU Affero General Public License v3.0](https://github.com/nextify-limited/libra/blob/main/LICENSE) open source license.

#### ✅ Your Rights

- **🆓 Free Use**: Free to use for personal, educational, and commercial projects
- **🔧 Modification and Customization**: Free to modify and extend code functionality
- **📤 Distribution and Sharing**: Can share with others under the same license
- **🏢 Commercial Deployment**: Allowed for commercial purpose deployment

#### 📋 Your Obligations

- **📄 Retain Copyright Information**: Must retain original copyright notices
- **🔓 Open Source Derivative Works**: Must provide source code for any modifications
- **📧 Network Service Open Source**: Must open source when providing services over network
- **🔗 Use Same License**: Derivative works must use AGPL-3.0 license

#### 💼 Commercial License

If you need more flexibility, we provide commercial licenses suitable for:

- Closed-source proprietary modifications
- Product distribution without open sourcing
- Customized licensing terms

Commercial licensing consultation: [<EMAIL>](mailto:<EMAIL>)

### 💬 Participate in Roadmap Discussion

We welcome community participation in roadmap planning:

- 📝 [Feature Requests](https://github.com/nextify-limited/libra/issues/new?template=feature_request.md)
- 💬 [Roadmap Discussion](https://github.com/nextify-limited/libra/discussions)
- 📧 [Enterprise Requirements](mailto:<EMAIL>)

### 🙏 Acknowledgments

Thanks to the following excellent sponsors for their valuable support to the Libra project, enabling us to focus on building better AI development experiences for developers:

**[Clerk](https://clerk.com?utm_source=libra.dev)** - The most comprehensive user management platform

**[E2B](https://e2b.dev/startups?utm_source=libra.dev)** - E2B is an open-source runtime environment for executing AI-generated code in secure cloud sandboxes, suitable for intelligent agents and AI application scenarios.

**[PostHog](https://posthog.com/startups?utm_source=libra.dev)** - A single platform for analytics, testing, observing, and deploying new features

**[Daytona](https://daytona.io/startups?utm_source=libra.dev)** - Daytona is a secure and resilient infrastructure for running AI-generated code

**[Cloudflare](https://www.cloudflare.com/forstartups/?utm_source=libra.dev)** - Global leading edge computing and network infrastructure provider

---