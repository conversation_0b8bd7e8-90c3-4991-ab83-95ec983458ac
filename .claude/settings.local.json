{"permissions": {"allow": ["Bash(ls:*)", "Bash(find:*)", "Bash(bun run:*)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "Bash(npm run build:packages:*)", "Bash(npm run:*)", "Bash(pnpm dev:*)", "Bash(bun:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git rebase:*)", "Bash(git reset:*)", "Bash(git checkout:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(gh pr list:*)", "Bash(gh pr view:*)", "Bash(git fetch:*)", "<PERSON><PERSON>(git rev-list:*)", "Bash(gh pr create:*)", "Bash(git merge-base:*)", "Bash(git pull:*)", "Bash(git stash:*)", "Bash(git merge:*)", "Bash(GIT_SEQUENCE_EDITOR=\"sed -i ''2,$s/pick/squash/''\" git rebase -i 176b597)"], "deny": []}}