# Libra AI Product Overview

Libra AI is an open-source AI-native development platform that enables full lifecycle management of web applications through natural language interaction. It serves as an alternative to V0/Lovable, specifically designed for the Cloudflare Workers ecosystem.

## Core Product Features

- **AI-Powered Code Generation**: Multi-model integration (Claude, OpenAI, Gemini, DeepSeek) for production-grade code generation
- **Integrated Development Experience**: Cloud IDE with syntax highlighting, HMR real-time preview, and intelligent dependency management
- **Full-Stack Integration**: Seamless GitHub integration, Cloudflare edge deployment, OAuth 2.0 authentication, and Stripe subscription management
- **Production Deployment**: Cloudflare Workers edge computing with serverless architecture, automated TLS/SSL, and Git version control

## Target Users

- **Individual Developers**: Rapid prototyping and AI-assisted development
- **Development Teams**: Collaborative project building with organization management
- **Enterprises**: Self-hosted deployment with complete data control and customization

## Business Model

- **Cloud Hosted Service**: SaaS platform at libra.dev with pay-as-you-go pricing
- **Open Source**: AGPL-3.0 licensed with commercial licensing available for enterprises
- **Self-Hosted**: Complete platform deployment on user infrastructure

## Key Differentiators

- **Cloudflare-Native**: Built specifically for Cloudflare Workers ecosystem (vs Vercel for V0)
- **Open Source**: Complete source code access and customization capabilities
- **Multi-Sandbox Support**: E2B and Daytona integration for secure code execution
- **Enterprise-Ready**: Self-hosted deployment options with complete data control