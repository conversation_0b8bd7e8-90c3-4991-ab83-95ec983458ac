{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2024", "lib": ["ES2024", "DOM", "DOM.Iterable"], "types": ["@cloudflare/workers-types"], "useDefineForClassFields": true, "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["cloudflare-env.d.ts"]}