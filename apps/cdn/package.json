{"name": "@libra/cdn", "version": "1.0.3", "scripts": {"dev": "wrangler dev --port 3004 --persist-to=../web/.wrangler/state", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "typecheck": "tsc --noEmit", "update": "bun update"}, "dependencies": {"@libra/auth": "*", "@libra/db": "*", "@libra/middleware": "*", "@libra/typescript-config": "*", "hono": "^4.9.5", "@scalar/hono-api-reference": "^0.9.16", "@hono/zod-openapi": "^0.19.10"}, "devDependencies": {"wrangler": "4.27.0"}}