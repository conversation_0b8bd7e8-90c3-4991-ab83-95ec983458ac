/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * image.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import { z } from 'zod/v4'

// Image request parameters schema
export const imageParamsSchema = z.object({
  key: z.string().describe('The image key to retrieve')
}).openapi('ImageParams')

// Image response headers schema
export const imageResponseHeadersSchema = z.object({
  'Content-Type': z.string().describe('MIME type of the image'),
  'Cache-Control': z.string().describe('Cache control header')
}).openapi('ImageResponseHeaders')

// Not found response schema
export const notFoundResponseSchema = z.object({
  error: z.literal('Not Found').describe('Error type'),
  message: z.string().describe('Error message')
}).openapi('NotFoundResponse')

// Image route types
export type ImageParams = z.infer<typeof imageParamsSchema>
export type ImageResponseHeaders = z.infer<typeof imageResponseHeadersSchema>
export type NotFoundResponse = z.infer<typeof notFoundResponseSchema>
