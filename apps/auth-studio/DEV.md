# @libra/auth-studio Development Guide

> Drizzle Studio Database Management Quick Tool - Development Documentation

Version: 1.0.0 - 2025-07-30

Last Updated: 2025-07-30

## Table of Contents

- [Overview](#overview)

## Overview

`apps/auth-studio` is a convenient launcher in the Libra project, used to quickly access the Drizzle Studio management interface of the authentication database. It is essentially a script shortcut, not a standalone application.

**Important Note**: This is not a complete application, but a quick entry to the database management tool provided by the `@libra/auth` package.

**Related Resources:**

- [`@libra/auth` package documentation](../../packages/auth/DEV_ZH.md)

- [Drizzle Studio Official Documentation](https://orm.drizzle.team/drizzle-studio/overview)

- [Project Main Documentation](../../CLAUDE.md)