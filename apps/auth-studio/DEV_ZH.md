# @libra/auth-studio 开发指南

> Drizzle Studio 数据库管理快捷工具 - 开发文档

版本：1.0.0 - 2025-07-30  
最后更新：2025-07-30

## 目录

- [概述](#概述)

## 概述

`apps/auth-studio` 是 Libra 项目中的一个便捷启动器，用于快速访问认证数据库的 Drizzle Studio 管理界面。它本质上是一个脚本快捷方式，而不是独立的应用程序。

**重要说明**: 这不是一个完整的应用程序，而是通过 `@libra/auth` 包提供的数据库管理工具的快速入口。

---

**相关资源:**
- [`@libra/auth` 包文档](../../packages/auth/DEV_ZH.md)
- [Drizzle Studio 官方文档](https://orm.drizzle.team/drizzle-studio/overview)
- [项目主文档](../../CLAUDE.md) 